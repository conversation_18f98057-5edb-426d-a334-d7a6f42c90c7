# OSM Data Visualization for Liaoning Province

This project provides tools to visualize OpenStreetMap (OSM) data from the Liaoning province PBF file using Python, Jupyter notebooks, and various geospatial libraries.

## Features

- **Load and parse PBF files** using osmium
- **Extract different OSM features**: roads, buildings, amenities, landuse, etc.
- **Create interactive maps** with Folium
- **Generate static plots** with Matplotlib and GeoPandas
- **Export data** to GeoJSON format
- **Analyze spatial data** with comprehensive statistics

## Setup Instructions

### 1. Create Conda Environment

```bash
# Create the environment from the provided environment.yml
conda env create -f environment.yml

# Activate the environment
conda activate osm-plotting
```

### 2. Alternative Manual Setup

If you prefer to set up manually:

```bash
# Create a new conda environment
conda create -n osm-plotting python=3.11

# Activate the environment
conda activate osm-plotting

# Install required packages
conda install -c conda-forge jupyter geopandas folium matplotlib seaborn plotly osmium pyosmium contextily

# Install additional packages with pip
pip install osmnx keplergl mapclassify branca
```

### 3. Start Jupyter

```bash
# Start Jupyter Lab
jupyter lab

# Or start Jupyter Notebook
jupyter notebook
```

### 4. Open the Notebook

Open `osm_plotting.ipynb` in Jupyter and run the cells in order.

## File Structure

```
polt-osm-china/
├── environment.yml          # Conda environment specification
├── osm_plotting.ipynb      # Main Jupyter notebook
├── liaoning.pbf            # OSM data file (binary format)
├── README.md               # This file
└── output/                 # Generated files (created when running notebook)
    ├── liaoning_osm_map.html
    ├── liaoning_highways.geojson
    ├── liaoning_buildings.geojson
    └── liaoning_amenities.geojson
```

## Usage

### Basic Usage

1. Ensure `liaoning.pbf` is in the project directory
2. Open `osm_plotting.ipynb` in Jupyter
3. Run all cells in order
4. The notebook will:
   - Load and parse the PBF file
   - Extract different types of OSM features
   - Create interactive maps and static plots
   - Export sample data to GeoJSON files

### Customization

#### Modify Feature Extraction

Edit the `OSMHandler` class to extract specific features:

```python
# Example: Extract only specific highway types
if 'highway' in way_data and way_data['highway'] in ['motorway', 'trunk', 'primary']:
    self.ways.append(way_data)
```

#### Adjust Visualization Parameters

```python
# Change map center and zoom
interactive_map = create_interactive_map(
    ways_gdf, nodes_gdf, 
    center_lat=42.0,  # Adjust latitude
    center_lon=124.0, # Adjust longitude
    zoom=10           # Adjust zoom level
)

# Modify sampling sizes for performance
sample_size = min(1000, len(ways_gdf))  # Reduce for better performance
```

#### Filter by Geographic Area

```python
# Filter data by bounding box
filtered_data = filter_by_bbox(ways_gdf, 
                              min_lon=120.0, min_lat=40.0,
                              max_lon=125.0, max_lat=43.0)
```

## Output Files

### Interactive Map
- **liaoning_osm_map.html**: Interactive map with multiple layers
  - Highways (red lines)
  - Buildings (blue polygons)
  - Amenities (green markers)
  - Multiple basemap options
  - Layer control for toggling features

### GeoJSON Exports
- **liaoning_highways.geojson**: Road network data
- **liaoning_buildings.geojson**: Building footprints
- **liaoning_amenities.geojson**: Points of interest

## Performance Considerations

### Large Files
- The Liaoning PBF file is substantial and may take several minutes to process
- Processing time depends on your system's RAM and CPU
- Consider using a subset of data for initial testing

### Memory Management
- Visualization functions automatically sample large datasets
- Adjust sample sizes in the code if you encounter memory issues
- Use bounding box filtering for specific regions of interest

### Optimization Tips
```python
# Reduce sample sizes for better performance
sample_size = min(1000, len(gdf))  # Instead of 5000

# Process specific feature types only
handler = OSMHandler(feature_types=['highway', 'building'])

# Use bounding box filtering
bbox_data = gdf.cx[120:125, 40:43]  # lon_min:lon_max, lat_min:lat_max
```

## Troubleshooting

### Common Issues

1. **Memory Error**: Reduce sample sizes in visualization functions
2. **File Not Found**: Ensure `liaoning.pbf` is in the correct directory
3. **Import Errors**: Verify all packages are installed in the conda environment
4. **Slow Performance**: Use smaller data samples or filter by geographic area

### Environment Issues

```bash
# If packages are missing
conda activate osm-plotting
conda install -c conda-forge missing-package-name

# If osmium installation fails
conda install -c conda-forge osmium-tool pyosmium

# Reset environment if needed
conda env remove -n osm-plotting
conda env create -f environment.yml
```

## Data Sources

- **OSM Data**: OpenStreetMap contributors
- **PBF File**: Liaoning province extract from OpenStreetMap
- **Coordinate System**: WGS84 (EPSG:4326)

## Dependencies

### Core Libraries
- **osmium/pyosmium**: PBF file reading
- **geopandas**: Geospatial data manipulation
- **folium**: Interactive mapping
- **matplotlib**: Static plotting
- **shapely**: Geometric operations

### Optional Libraries
- **contextily**: Basemap tiles
- **osmnx**: Additional OSM tools
- **plotly**: Interactive plotting
- **keplergl**: Advanced visualization

## License

This project uses OpenStreetMap data, which is available under the Open Database License (ODbL).

## Contributing

Feel free to submit issues and enhancement requests!
