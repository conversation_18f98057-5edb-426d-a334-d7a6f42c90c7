# Import required libraries
import osmium
import pandas as pd
import geopandas as gpd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import folium
from shapely.geometry import Point, LineString, Polygon
import contextily as ctx
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# Set up plotting style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

print("Libraries imported successfully!")

class OSMHandler(osmium.SimpleHandler):
    """
    Handler class to extract different types of OSM features from PBF files
    """
    
    def __init__(self, feature_types=['all']):
        osmium.SimpleHandler.__init__(self)
        self.feature_types = feature_types
        self.nodes = []
        self.ways = []
        self.relations = []
        self.node_coords = {}  # Store node coordinates for way reconstruction
        
    def node(self, n):
        """Process nodes"""
        # Store coordinates for later use in ways
        self.node_coords[n.id] = (n.location.lon, n.location.lat)
        
        # Extract nodes with tags (POIs, amenities, etc.)
        if len(n.tags) > 0:
            node_data = {
                'id': n.id,
                'lon': n.location.lon,
                'lat': n.location.lat,
                'geometry': Point(n.location.lon, n.location.lat)
            }
            
            # Add all tags
            for tag in n.tags:
                node_data[tag.k] = tag.v
                
            self.nodes.append(node_data)
    
    def way(self, w):
        """Process ways (roads, buildings, etc.)"""
        if len(w.tags) > 0 and len(w.nodes) > 1:
            # Get coordinates for all nodes in the way
            coords = []
            for node_ref in w.nodes:
                if node_ref.ref in self.node_coords:
                    coords.append(self.node_coords[node_ref.ref])
            
            if len(coords) > 1:
                way_data = {
                    'id': w.id,
                    'nodes': [n.ref for n in w.nodes]
                }
                
                # Create geometry
                if coords[0] == coords[-1] and len(coords) > 3:
                    # Closed way - polygon
                    way_data['geometry'] = Polygon(coords)
                else:
                    # Open way - linestring
                    way_data['geometry'] = LineString(coords)
                
                # Add all tags
                for tag in w.tags:
                    way_data[tag.k] = tag.v
                    
                self.ways.append(way_data)
    
    def relation(self, r):
        """Process relations"""
        if len(r.tags) > 0:
            relation_data = {
                'id': r.id,
                'members': [(m.type, m.ref, m.role) for m in r.members]
            }
            
            # Add all tags
            for tag in r.tags:
                relation_data[tag.k] = tag.v
                
            self.relations.append(relation_data)

print("OSM Handler class defined!")

# Path to the PBF file
pbf_file = "liaoning.pbf"

print(f"Loading OSM data from {pbf_file}...")
print("This may take several minutes for large files.")

# Create handler and process file
handler = OSMHandler()

try:
    handler.apply_file(pbf_file)
    print(f"\nData loaded successfully!")
    print(f"Nodes with tags: {len(handler.nodes)}")
    print(f"Ways: {len(handler.ways)}")
    print(f"Relations: {len(handler.relations)}")
    print(f"Total node coordinates stored: {len(handler.node_coords)}")
except Exception as e:
    print(f"Error loading file: {e}")
    print("Make sure the liaoning.pbf file is in the current directory.")

# Convert to GeoDataFrames for easier analysis
if len(handler.nodes) > 0:
    nodes_gdf = gpd.GeoDataFrame(handler.nodes, crs='EPSG:4326')
    print(f"Nodes GeoDataFrame created: {len(nodes_gdf)} features")
    print(f"Node columns: {list(nodes_gdf.columns)}")
else:
    nodes_gdf = None
    print("No nodes with tags found")

if len(handler.ways) > 0:
    ways_gdf = gpd.GeoDataFrame(handler.ways, crs='EPSG:4326')
    print(f"\nWays GeoDataFrame created: {len(ways_gdf)} features")
    print(f"Way columns: {list(ways_gdf.columns)}")
else:
    ways_gdf = None
    print("No ways found")

if len(handler.relations) > 0:
    relations_df = pd.DataFrame(handler.relations)
    print(f"\nRelations DataFrame created: {len(relations_df)} features")
    print(f"Relation columns: {list(relations_df.columns)}")
else:
    relations_df = None
    print("No relations found")

# Analyze the data structure
if ways_gdf is not None:
    print("=== WAY ANALYSIS ===")
    
    # Check what types of features we have
    if 'highway' in ways_gdf.columns:
        highway_types = ways_gdf['highway'].value_counts().head(10)
        print(f"\nTop highway types:")
        print(highway_types)
    
    if 'building' in ways_gdf.columns:
        building_count = ways_gdf['building'].notna().sum()
        print(f"\nBuildings: {building_count}")
        if building_count > 0:
            building_types = ways_gdf[ways_gdf['building'].notna()]['building'].value_counts().head(10)
            print("Building types:")
            print(building_types)
    
    if 'landuse' in ways_gdf.columns:
        landuse_count = ways_gdf['landuse'].notna().sum()
        print(f"\nLanduse areas: {landuse_count}")
        if landuse_count > 0:
            landuse_types = ways_gdf[ways_gdf['landuse'].notna()]['landuse'].value_counts().head(10)
            print("Landuse types:")
            print(landuse_types)
    
    if 'natural' in ways_gdf.columns:
        natural_count = ways_gdf['natural'].notna().sum()
        print(f"\nNatural features: {natural_count}")
        if natural_count > 0:
            natural_types = ways_gdf[ways_gdf['natural'].notna()]['natural'].value_counts().head(10)
            print("Natural feature types:")
            print(natural_types)

if nodes_gdf is not None:
    print("\n=== NODE ANALYSIS ===")
    
    if 'amenity' in nodes_gdf.columns:
        amenity_count = nodes_gdf['amenity'].notna().sum()
        print(f"\nAmenities: {amenity_count}")
        if amenity_count > 0:
            amenity_types = nodes_gdf[nodes_gdf['amenity'].notna()]['amenity'].value_counts().head(10)
            print("Amenity types:")
            print(amenity_types)
    
    if 'shop' in nodes_gdf.columns:
        shop_count = nodes_gdf['shop'].notna().sum()
        print(f"\nShops: {shop_count}")
        if shop_count > 0:
            shop_types = nodes_gdf[nodes_gdf['shop'].notna()]['shop'].value_counts().head(10)
            print("Shop types:")
            print(shop_types)

def create_interactive_map(ways_gdf=None, nodes_gdf=None, center_lat=41.8, center_lon=123.4, zoom=8):
    """
    Create an interactive map with OSM data
    """
    # Create base map centered on Liaoning
    m = folium.Map(
        location=[center_lat, center_lon],
        zoom_start=zoom,
        tiles='OpenStreetMap'
    )
    
    # Add different tile layers
    folium.TileLayer('Stamen Terrain').add_to(m)
    folium.TileLayer('CartoDB positron').add_to(m)
    
    # Add ways (roads, buildings, etc.)
    if ways_gdf is not None and len(ways_gdf) > 0:
        print("Adding ways to map...")
        
        # Sample data if too large
        sample_size = min(5000, len(ways_gdf))
        ways_sample = ways_gdf.sample(n=sample_size) if len(ways_gdf) > sample_size else ways_gdf
        
        # Add highways
        if 'highway' in ways_sample.columns:
            highways = ways_sample[ways_sample['highway'].notna()]
            if len(highways) > 0:
                highway_group = folium.FeatureGroup(name='Highways')
                for idx, row in highways.iterrows():
                    if row.geometry.geom_type == 'LineString':
                        coords = [[lat, lon] for lon, lat in row.geometry.coords]
                        folium.PolyLine(
                            coords,
                            color='red',
                            weight=2,
                            popup=f"Highway: {row.get('highway', 'Unknown')}"
                        ).add_to(highway_group)
                highway_group.add_to(m)
        
        # Add buildings
        if 'building' in ways_sample.columns:
            buildings = ways_sample[ways_sample['building'].notna()]
            if len(buildings) > 0:
                building_group = folium.FeatureGroup(name='Buildings')
                building_sample = buildings.sample(n=min(1000, len(buildings)))
                for idx, row in building_sample.iterrows():
                    if row.geometry.geom_type == 'Polygon':
                        coords = [[lat, lon] for lon, lat in row.geometry.exterior.coords]
                        folium.Polygon(
                            coords,
                            color='blue',
                            weight=1,
                            fillColor='lightblue',
                            fillOpacity=0.5,
                            popup=f"Building: {row.get('building', 'Yes')}"
                        ).add_to(building_group)
                building_group.add_to(m)
    
    # Add nodes (POIs, amenities)
    if nodes_gdf is not None and len(nodes_gdf) > 0:
        print("Adding nodes to map...")
        
        # Sample nodes if too many
        sample_size = min(2000, len(nodes_gdf))
        nodes_sample = nodes_gdf.sample(n=sample_size) if len(nodes_gdf) > sample_size else nodes_gdf
        
        # Add amenities
        if 'amenity' in nodes_sample.columns:
            amenities = nodes_sample[nodes_sample['amenity'].notna()]
            if len(amenities) > 0:
                amenity_group = folium.FeatureGroup(name='Amenities')
                for idx, row in amenities.iterrows():
                    folium.CircleMarker(
                        location=[row.lat, row.lon],
                        radius=3,
                        color='green',
                        fillColor='lightgreen',
                        fillOpacity=0.7,
                        popup=f"Amenity: {row.get('amenity', 'Unknown')}<br>Name: {row.get('name', 'N/A')}"
                    ).add_to(amenity_group)
                amenity_group.add_to(m)
    
    # Add layer control
    folium.LayerControl().add_to(m)
    
    return m

# Create the interactive map
if ways_gdf is not None or nodes_gdf is not None:
    print("Creating interactive map...")
    interactive_map = create_interactive_map(ways_gdf, nodes_gdf)
    
    # Save the map
    interactive_map.save('liaoning_osm_map.html')
    print("Interactive map saved as 'liaoning_osm_map.html'")
    
    # Display the map
    interactive_map
else:
    print("No data available for mapping")

def plot_osm_features(ways_gdf=None, nodes_gdf=None, feature_type='highway', figsize=(15, 10)):
    """
    Create static plots of OSM features
    """
    fig, axes = plt.subplots(1, 2, figsize=figsize)
    
    if ways_gdf is not None and feature_type in ways_gdf.columns:
        # Plot ways
        ax1 = axes[0]
        feature_data = ways_gdf[ways_gdf[feature_type].notna()]
        
        if len(feature_data) > 0:
            # Sample if too large
            if len(feature_data) > 10000:
                feature_data = feature_data.sample(n=10000)
            
            feature_data.plot(ax=ax1, linewidth=0.5, alpha=0.7)
            ax1.set_title(f'{feature_type.title()} Features')
            ax1.set_xlabel('Longitude')
            ax1.set_ylabel('Latitude')
            
            # Add basemap if possible
            try:
                ctx.add_basemap(ax1, crs=feature_data.crs.to_string(), source=ctx.providers.CartoDB.Positron)
            except:
                pass
        else:
            ax1.text(0.5, 0.5, f'No {feature_type} data found', 
                    ha='center', va='center', transform=ax1.transAxes)
    else:
        axes[0].text(0.5, 0.5, 'No way data available', 
                    ha='center', va='center', transform=axes[0].transAxes)
    
    # Plot feature distribution
    ax2 = axes[1]
    if ways_gdf is not None and feature_type in ways_gdf.columns:
        feature_counts = ways_gdf[feature_type].value_counts().head(15)
        if len(feature_counts) > 0:
            feature_counts.plot(kind='bar', ax=ax2)
            ax2.set_title(f'{feature_type.title()} Distribution')
            ax2.set_xlabel(f'{feature_type.title()} Type')
            ax2.set_ylabel('Count')
            ax2.tick_params(axis='x', rotation=45)
        else:
            ax2.text(0.5, 0.5, f'No {feature_type} data found', 
                    ha='center', va='center', transform=ax2.transAxes)
    else:
        ax2.text(0.5, 0.5, 'No data available', 
                ha='center', va='center', transform=ax2.transAxes)
    
    plt.tight_layout()
    plt.show()

# Create plots for different features
if ways_gdf is not None:
    # Plot highways
    if 'highway' in ways_gdf.columns:
        print("Plotting highway network...")
        plot_osm_features(ways_gdf, feature_type='highway')
    
    # Plot buildings
    if 'building' in ways_gdf.columns:
        print("Plotting buildings...")
        plot_osm_features(ways_gdf, feature_type='building')
    
    # Plot landuse
    if 'landuse' in ways_gdf.columns:
        print("Plotting landuse...")
        plot_osm_features(ways_gdf, feature_type='landuse')
else:
    print("No way data available for plotting")

def get_bounding_box(gdf):
    """
    Get the bounding box of a GeoDataFrame
    """
    bounds = gdf.total_bounds
    return {
        'min_lon': bounds[0],
        'min_lat': bounds[1],
        'max_lon': bounds[2],
        'max_lat': bounds[3],
        'center_lon': (bounds[0] + bounds[2]) / 2,
        'center_lat': (bounds[1] + bounds[3]) / 2
    }

def filter_by_bbox(gdf, min_lon, min_lat, max_lon, max_lat):
    """
    Filter GeoDataFrame by bounding box
    """
    return gdf.cx[min_lon:max_lon, min_lat:max_lat]

def export_to_geojson(gdf, filename, sample_size=None):
    """
    Export GeoDataFrame to GeoJSON
    """
    if sample_size and len(gdf) > sample_size:
        gdf = gdf.sample(n=sample_size)
    
    gdf.to_file(filename, driver='GeoJSON')
    print(f"Exported {len(gdf)} features to {filename}")

def create_summary_stats(ways_gdf=None, nodes_gdf=None):
    """
    Create summary statistics
    """
    stats = {}
    
    if ways_gdf is not None:
        stats['ways'] = {
            'total_count': len(ways_gdf),
            'bbox': get_bounding_box(ways_gdf)
        }
        
        # Count different feature types
        for col in ['highway', 'building', 'landuse', 'natural', 'amenity']:
            if col in ways_gdf.columns:
                count = ways_gdf[col].notna().sum()
                stats['ways'][f'{col}_count'] = count
                if count > 0:
                    stats['ways'][f'{col}_types'] = ways_gdf[col].value_counts().to_dict()
    
    if nodes_gdf is not None:
        stats['nodes'] = {
            'total_count': len(nodes_gdf),
            'bbox': get_bounding_box(nodes_gdf)
        }
        
        # Count different feature types
        for col in ['amenity', 'shop', 'tourism', 'leisure']:
            if col in nodes_gdf.columns:
                count = nodes_gdf[col].notna().sum()
                stats['nodes'][f'{col}_count'] = count
                if count > 0:
                    stats['nodes'][f'{col}_types'] = nodes_gdf[col].value_counts().to_dict()
    
    return stats

# Generate summary statistics
if ways_gdf is not None or nodes_gdf is not None:
    print("Generating summary statistics...")
    summary_stats = create_summary_stats(ways_gdf, nodes_gdf)
    
    # Print summary
    print("\n=== SUMMARY STATISTICS ===")
    for data_type, stats in summary_stats.items():
        print(f"\n{data_type.upper()}:")
        print(f"  Total count: {stats['total_count']}")
        if 'bbox' in stats:
            bbox = stats['bbox']
            print(f"  Bounding box: ({bbox['min_lon']:.4f}, {bbox['min_lat']:.4f}) to ({bbox['max_lon']:.4f}, {bbox['max_lat']:.4f})")
            print(f"  Center: ({bbox['center_lon']:.4f}, {bbox['center_lat']:.4f})")
        
        # Print feature counts
        for key, value in stats.items():
            if key.endswith('_count') and value > 0:
                feature_type = key.replace('_count', '')
                print(f"  {feature_type}: {value}")
else:
    print("No data available for summary statistics")

# Export sample data to different formats
print("Exporting data samples...")

if ways_gdf is not None:
    # Export highways
    if 'highway' in ways_gdf.columns:
        highways = ways_gdf[ways_gdf['highway'].notna()]
        if len(highways) > 0:
            export_to_geojson(highways, 'liaoning_highways.geojson', sample_size=5000)
    
    # Export buildings
    if 'building' in ways_gdf.columns:
        buildings = ways_gdf[ways_gdf['building'].notna()]
        if len(buildings) > 0:
            export_to_geojson(buildings, 'liaoning_buildings.geojson', sample_size=2000)

if nodes_gdf is not None:
    # Export amenities
    if 'amenity' in nodes_gdf.columns:
        amenities = nodes_gdf[nodes_gdf['amenity'].notna()]
        if len(amenities) > 0:
            export_to_geojson(amenities, 'liaoning_amenities.geojson', sample_size=3000)

print("\nExport complete! Files created:")
print("- liaoning_osm_map.html (interactive map)")
print("- liaoning_highways.geojson (highway network)")
print("- liaoning_buildings.geojson (building footprints)")
print("- liaoning_amenities.geojson (points of interest)")