#!/usr/bin/env python3
"""
Test script to verify the OSM plotting environment setup
"""

import sys
import os

def test_imports():
    """Test that all required packages can be imported"""
    print("Testing package imports...")

    try:
        import osmium
        print("✓ osmium imported successfully")
    except ImportError as e:
        print(f"✗ osmium import failed: {e}")
        return False

    try:
        import pandas as pd
        import geopandas as gpd
        print("✓ pandas and geopandas imported successfully")
    except ImportError as e:
        print(f"✗ pandas/geopandas import failed: {e}")
        return False

    try:
        import matplotlib.pyplot as plt
        import seaborn as sns
        print("✓ matplotlib and seaborn imported successfully")
    except ImportError as e:
        print(f"✗ matplotlib/seaborn import failed: {e}")
        return False

    try:
        import folium
        print("✓ folium imported successfully")
    except ImportError as e:
        print(f"✗ folium import failed: {e}")
        return False

    try:
        from shapely.geometry import Point, LineString, Polygon
        print("✓ shapely imported successfully")
    except ImportError as e:
        print(f"✗ shapely import failed: {e}")
        return False

    try:
        import contextily as ctx
        print("✓ contextily imported successfully")
    except ImportError as e:
        print(f"✗ contextily import failed: {e}")
        return False

    return True

def test_pbf_file():
    """Test that the PBF file exists and is readable"""
    print("\nTesting PBF file...")

    pbf_file = "liaoning.pbf"
    if not os.path.exists(pbf_file):
        print(f"✗ PBF file not found: {pbf_file}")
        return False

    file_size = os.path.getsize(pbf_file)
    print(f"✓ PBF file found: {pbf_file} ({file_size / (1024*1024):.1f} MB)")

    # Test if we can create an osmium handler
    try:
        import osmium

        class TestHandler(osmium.SimpleHandler):
            def __init__(self):
                osmium.SimpleHandler.__init__(self)
                self.node_count = 0
                self.way_count = 0
                self.max_nodes = 10

            def node(self, n):
                self.node_count += 1
                if self.node_count >= self.max_nodes:  # Stop after 10 nodes for testing
                    return

            def way(self, w):
                self.way_count += 1

        handler = TestHandler()
        handler.apply_file(pbf_file)

        print(f"✓ PBF file is readable (tested {handler.node_count} nodes, {handler.way_count} ways)")
        return True

    except Exception as e:
        print(f"✗ Error reading PBF file: {e}")
        return False

def test_basic_functionality():
    """Test basic geospatial functionality"""
    print("\nTesting basic functionality...")

    try:
        import geopandas as gpd
        from shapely.geometry import Point
        import pandas as pd

        # Create a simple GeoDataFrame
        data = {
            'name': ['Point A', 'Point B'],
            'geometry': [Point(120.0, 41.0), Point(121.0, 42.0)]
        }
        gdf = gpd.GeoDataFrame(data, crs='EPSG:4326')

        print(f"✓ Created GeoDataFrame with {len(gdf)} points")

        # Test basic operations
        bounds = gdf.total_bounds
        print(f"✓ Calculated bounds: {bounds}")

        # Test folium map creation
        import folium
        m = folium.Map(location=[41.5, 120.5], zoom_start=8)
        print("✓ Created folium map")

        return True

    except Exception as e:
        print(f"✗ Basic functionality test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("OSM Plotting Environment Test")
    print("=" * 40)

    all_passed = True

    # Test imports
    if not test_imports():
        all_passed = False

    # Test PBF file
    if not test_pbf_file():
        all_passed = False

    # Test basic functionality
    if not test_basic_functionality():
        all_passed = False

    print("\n" + "=" * 40)
    if all_passed:
        print("✓ All tests passed! Environment is ready for OSM plotting.")
        print("\nNext steps:")
        print("1. Start Jupyter Lab: conda activate osm-plotting && jupyter lab")
        print("2. Open osm_plotting.ipynb")
        print("3. Run the cells to process and visualize the OSM data")
    else:
        print("✗ Some tests failed. Please check the setup.")
        sys.exit(1)

if __name__ == "__main__":
    main()
